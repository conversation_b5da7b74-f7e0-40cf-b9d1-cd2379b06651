from langchain.agents import Tool
from prompts.job_prompt import <PERSON>OMPT
import os
from tavily import TavilyClient

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"')
    except FileNotFoundError:
        pass
    except Exception as e:
        pass

# Load environment variables
load_env()

# Initialize Tavily client with error handling
try:
    tavily_key = os.getenv("TAVILY_API_KEY")
    if tavily_key:
        client = TavilyClient(api_key=tavily_key)
        print("✅ Tavily client initialized successfully")
    else:
        print("⚠️ TAVILY_API_KEY not found in environment variables")
        client = None
except Exception as e:
    print(f"⚠️ Tavily client initialization failed: {e}")
    client = None

def search_jobs(parsed_resume):
    tech_stack = parsed_resume.get("Technical Skills", [])

    if not client:
        # Return mock job data if Tavily client is not available
        return [
            {
                "title": f"Software Engineer - {tech_stack[0] if tech_stack else 'Python'}",
                "url": "https://linkedin.com/jobs/example1",
                "content": "Exciting opportunity for a software engineer with experience in modern technologies."
            },
            {
                "title": f"Full Stack Developer - {tech_stack[1] if len(tech_stack) > 1 else 'JavaScript'}",
                "url": "https://linkedin.com/jobs/example2",
                "content": "Join our team as a full stack developer working with cutting-edge technologies."
            },
            {
                "title": "Senior Software Engineer",
                "url": "https://linkedin.com/jobs/example3",
                "content": "Lead technical initiatives in a fast-growing startup environment."
            }
        ]

    try:
        query = f"Software jobs for {', '.join(tech_stack)} site:linkedin.com/jobs"
        result = client.search(query=query)
        return result.get("results", [])[:10]
    except Exception as e:
        print(f"Job search error: {e}")
        # Return mock data on error
        return [
            {
                "title": "Software Engineer Position",
                "url": "https://linkedin.com/jobs/fallback",
                "content": "Job search temporarily unavailable. Please try again later."
            }
        ]

job_search_tool = Tool(
    name="JobFinder",
    func=search_jobs,
    description="Search jobs matching the user's tech stack"
)

def job_node(state):
    if "email" not in state["history"]:
        state["history"]["email"] = "<EMAIL>"  # fallback if needed
    jobs = search_jobs(state["parsed"])
    state["jobs"] = jobs
    return state