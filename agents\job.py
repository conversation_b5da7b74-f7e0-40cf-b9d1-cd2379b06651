from langchain.agents import Tool
from prompts.job_prompt import PROMPT
import os
from tavily import TavilyClient

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"')
    except FileNotFoundError:
        pass
    except Exception as e:
        pass

# Load environment variables
load_env()

# Initialize Tavily client with error handling
try:
    tavily_key = os.getenv("TAVILY_API_KEY")
    if tavily_key:
        client = TavilyClient(api_key=tavily_key)
        print("✅ Tavily client initialized successfully")
    else:
        print("⚠️ TAVILY_API_KEY not found in environment variables")
        client = None
except Exception as e:
    print(f"⚠️ Tavily client initialization failed: {e}")
    client = None

def search_jobs(parsed_resume):
    """Enhanced job search with broader company and platform coverage"""
    tech_stack = parsed_resume.get("Technical Skills", [])
    experience = parsed_resume.get("experience", [])
    education = parsed_resume.get("education", [])

    # Determine experience level
    experience_level = "entry" if len(experience) <= 1 else "mid" if len(experience) <= 3 else "senior"

    # Build comprehensive search queries
    search_queries = []

    # Primary tech stack searches
    if tech_stack:
        primary_skills = tech_stack[:3]  # Top 3 skills
        search_queries.extend([
            f"{' '.join(primary_skills)} developer jobs site:linkedin.com",
            f"{' '.join(primary_skills)} engineer jobs site:indeed.com",
            f"{primary_skills[0]} {experience_level} developer site:glassdoor.com",
            f"{primary_skills[0]} jobs site:dice.com",
            f"{primary_skills[0]} remote jobs site:remote.co"
        ])

    # Company-specific searches
    major_companies = [
        "Google", "Microsoft", "Amazon", "Apple", "Meta", "Netflix", "Tesla", "Uber",
        "Airbnb", "Spotify", "Adobe", "Salesforce", "Oracle", "IBM", "Intel", "NVIDIA",
        "JPMorgan", "Goldman Sachs", "Morgan Stanley", "Accenture", "Deloitte",
        "McKinsey", "Boeing", "Lockheed Martin", "Johnson & Johnson", "Pfizer"
    ]

    if tech_stack:
        for company in major_companies[:5]:  # Search top 5 companies
            search_queries.append(f"{tech_stack[0]} jobs {company} careers")

    # Industry-specific searches
    if any(skill.lower() in ['python', 'java', 'javascript', 'react', 'node'] for skill in tech_stack):
        search_queries.extend([
            "software engineer jobs fintech",
            "full stack developer startup jobs",
            "backend developer remote jobs",
            "frontend developer jobs"
        ])

    if any(skill.lower() in ['data', 'machine learning', 'ai', 'analytics'] for skill in tech_stack):
        search_queries.extend([
            "data scientist jobs",
            "machine learning engineer jobs",
            "AI researcher positions",
            "data analyst remote jobs"
        ])

    if any(skill.lower() in ['devops', 'aws', 'docker', 'kubernetes'] for skill in tech_stack):
        search_queries.extend([
            "devops engineer jobs",
            "cloud engineer positions",
            "site reliability engineer jobs"
        ])

    all_jobs = []

    if not client:
        # Enhanced mock job data based on skills
        mock_jobs = generate_mock_jobs(tech_stack, experience_level)
        return mock_jobs

    try:
        # Execute multiple searches for comprehensive results
        for query in search_queries[:8]:  # Limit to 8 queries to avoid rate limits
            try:
                result = client.search(query=query, max_results=3)
                jobs = result.get("results", [])

                # Process and enhance job data
                for job in jobs:
                    enhanced_job = {
                        "title": job.get("title", "Software Position"),
                        "url": job.get("url", ""),
                        "content": job.get("content", "")[:200] + "...",
                        "company": extract_company_from_url(job.get("url", "")),
                        "source": extract_source_from_url(job.get("url", "")),
                        "relevance_score": calculate_relevance_score(job, tech_stack)
                    }
                    all_jobs.append(enhanced_job)

            except Exception as e:
                print(f"Search query failed: {query} - {e}")
                continue

        # Remove duplicates and sort by relevance
        unique_jobs = remove_duplicate_jobs(all_jobs)
        sorted_jobs = sorted(unique_jobs, key=lambda x: x.get("relevance_score", 0), reverse=True)

        return sorted_jobs[:15]  # Return top 15 jobs

    except Exception as e:
        print(f"Job search error: {e}")
        # Return enhanced mock data on error
        return generate_mock_jobs(tech_stack, experience_level)

def generate_mock_jobs(tech_stack, experience_level):
    """Generate realistic mock job data based on user's skills"""
    primary_skill = tech_stack[0] if tech_stack else "Software"

    companies = [
        "Google", "Microsoft", "Amazon", "Apple", "Meta", "Netflix", "Uber", "Airbnb",
        "Spotify", "Adobe", "Salesforce", "Oracle", "IBM", "Intel", "NVIDIA",
        "JPMorgan Chase", "Goldman Sachs", "Accenture", "Deloitte", "McKinsey",
        "Tesla", "SpaceX", "Boeing", "Stripe", "PayPal", "Square", "Zoom", "Slack"
    ]

    job_titles = [
        f"{experience_level.title()} {primary_skill} Developer",
        f"{primary_skill} Software Engineer",
        f"Full Stack Developer - {primary_skill}",
        f"{experience_level.title()} Software Engineer",
        f"{primary_skill} Backend Developer",
        f"Frontend Developer - {primary_skill}",
        f"{experience_level.title()} DevOps Engineer",
        f"Cloud Engineer - {primary_skill}",
        f"Data Engineer - {primary_skill}",
        f"{experience_level.title()} Product Engineer"
    ]

    platforms = [
        "linkedin.com/jobs", "indeed.com", "glassdoor.com", "dice.com",
        "remote.co", "wellfound.com", "stackoverflow.com/jobs"
    ]

    mock_jobs = []
    for i in range(12):
        company = companies[i % len(companies)]
        title = job_titles[i % len(job_titles)]
        platform = platforms[i % len(platforms)]

        mock_jobs.append({
            "title": title,
            "company": company,
            "url": f"https://{platform}/view/{company.lower().replace(' ', '-')}-{i+1}",
            "content": f"Exciting {experience_level}-level opportunity at {company} working with {primary_skill} and modern technologies. Join our innovative team!",
            "source": platform.split('.')[0].title(),
            "relevance_score": 85 - (i * 2)  # Decreasing relevance
        })

    return mock_jobs

def extract_company_from_url(url):
    """Extract company name from job URL"""
    if not url:
        return "Unknown Company"

    # Common patterns for extracting company names
    if "careers." in url:
        domain = url.split("careers.")[1].split("/")[0]
        return domain.split(".")[0].title()
    elif "jobs." in url:
        domain = url.split("jobs.")[1].split("/")[0]
        return domain.split(".")[0].title()
    else:
        domain = url.split("//")[1].split("/")[0] if "//" in url else url.split("/")[0]
        return domain.split(".")[0].title()

def extract_source_from_url(url):
    """Extract job board/source from URL"""
    if not url:
        return "Direct"

    if "linkedin.com" in url:
        return "LinkedIn"
    elif "indeed.com" in url:
        return "Indeed"
    elif "glassdoor.com" in url:
        return "Glassdoor"
    elif "dice.com" in url:
        return "Dice"
    elif "remote.co" in url:
        return "Remote.co"
    elif "wellfound.com" in url:
        return "Wellfound"
    else:
        return "Company Website"

def calculate_relevance_score(job, tech_stack):
    """Calculate relevance score based on job content and user skills"""
    score = 50  # Base score

    title = job.get("title", "").lower()
    content = job.get("content", "").lower()

    # Boost score for matching skills
    for skill in tech_stack:
        if skill.lower() in title:
            score += 15
        elif skill.lower() in content:
            score += 10

    # Boost for senior positions if user has experience
    if "senior" in title or "lead" in title:
        score += 10

    # Boost for remote opportunities
    if "remote" in title or "remote" in content:
        score += 5

    return min(score, 100)  # Cap at 100

def remove_duplicate_jobs(jobs):
    """Remove duplicate job postings based on title and company"""
    seen = set()
    unique_jobs = []

    for job in jobs:
        key = (job.get("title", "").lower(), job.get("company", "").lower())
        if key not in seen:
            seen.add(key)
            unique_jobs.append(job)

    return unique_jobs

job_search_tool = Tool(
    name="JobFinder",
    func=search_jobs,
    description="Search jobs matching the user's tech stack"
)

def job_node(state):
    if "email" not in state["history"]:
        state["history"]["email"] = "<EMAIL>"  # fallback if needed
    jobs = search_jobs(state["parsed"])
    state["jobs"] = jobs
    return state