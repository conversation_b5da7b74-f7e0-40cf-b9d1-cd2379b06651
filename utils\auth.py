"""
Authentication utilities for JWT-based user management
"""

import os
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import Crypt<PERSON>ontext
from jose import JWTError, jwt
from fastapi import HTTPException, status
import uuid

# JWT Configuration
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30 * 24 * 60  # 30 days

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# User database file
USERS_FILE = "db/users.json"

class AuthManager:
    def __init__(self):
        self.users_file = USERS_FILE
        os.makedirs("db", exist_ok=True)
    
    def load_users(self) -> Dict:
        """Load users from JSON file"""
        if not os.path.exists(self.users_file):
            return {}
        try:
            with open(self.users_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}
    
    def save_users(self, users: Dict) -> None:
        """Save users to JSON file"""
        with open(self.users_file, "w", encoding="utf-8") as f:
            json.dump(users, f, indent=2, ensure_ascii=False)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Hash a password"""
        return pwd_context.hash(password)
    
    def create_user(self, email: str, password: str, full_name: str = None) -> Dict[str, Any]:
        """Create a new user account"""
        users = self.load_users()
        
        # Check if user already exists
        if email in users:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Create user
        user_id = str(uuid.uuid4())
        hashed_password = self.get_password_hash(password)
        
        user_data = {
            "user_id": user_id,
            "email": email,
            "full_name": full_name or email.split("@")[0].title(),
            "hashed_password": hashed_password,
            "created_at": datetime.now().isoformat(),
            "is_active": True,
            "sessions": []  # List of session IDs associated with this user
        }
        
        users[email] = user_data
        self.save_users(users)
        
        # Remove password from return data
        user_data_safe = user_data.copy()
        del user_data_safe["hashed_password"]
        
        return user_data_safe
    
    def authenticate_user(self, email: str, password: str) -> Optional[Dict]:
        """Authenticate user with email and password"""
        users = self.load_users()
        user = users.get(email)
        
        if not user:
            return None
        
        if not self.verify_password(password, user["hashed_password"]):
            return None
        
        # Remove password from return data
        user_safe = user.copy()
        del user_safe["hashed_password"]
        
        return user_safe
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """Get user by email"""
        users = self.load_users()
        user = users.get(email)
        
        if user:
            user_safe = user.copy()
            del user_safe["hashed_password"]
            return user_safe
        
        return None
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """Create JWT access token"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict]:
        """Verify JWT token and return user data"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            email: str = payload.get("sub")
            
            if email is None:
                return None
            
            user = self.get_user_by_email(email)
            return user
            
        except JWTError:
            return None
    
    def add_session_to_user(self, email: str, session_id: str) -> bool:
        """Add a session ID to user's session list"""
        users = self.load_users()
        
        if email not in users:
            return False
        
        if session_id not in users[email]["sessions"]:
            users[email]["sessions"].append(session_id)
            self.save_users(users)
        
        return True
    
    def get_user_sessions(self, email: str) -> list:
        """Get all session IDs for a user"""
        users = self.load_users()
        user = users.get(email)
        
        if user:
            return user.get("sessions", [])
        
        return []
    
    def update_user_profile(self, email: str, updates: Dict) -> bool:
        """Update user profile information"""
        users = self.load_users()
        
        if email not in users:
            return False
        
        # Only allow certain fields to be updated
        allowed_fields = ["full_name"]
        for field in allowed_fields:
            if field in updates:
                users[email][field] = updates[field]
        
        users[email]["updated_at"] = datetime.now().isoformat()
        self.save_users(users)
        
        return True

# Global auth manager instance
auth_manager = AuthManager()

# Pydantic models for requests
from pydantic import BaseModel, EmailStr

class UserCreate(BaseModel):
    email: EmailStr
    password: str
    full_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    user: Dict[str, Any]

class TokenData(BaseModel):
    email: Optional[str] = None
