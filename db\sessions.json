{"f361e171-376a-4577-a825-8d0ea0f6c265": {"user_name": "Test User", "email": "<EMAIL>", "created_at": "2025-07-12T21:49:46.733388", "last_accessed": "2025-07-12T21:49:46.749185", "resume_data": {"filename": "test_resume.pdf", "content": "dGVzdCBjb250ZW50", "content_type": "base64"}, "conversation_history": [], "improvement_suggestions": [], "job_recommendations": [], "context": {"email_provided": true, "resume_parsed": false, "jobs_searched": false}}, "2f44b890-6c0c-4598-aaf7-b49ce13036dd": {"user_name": "Test User", "email": "<EMAIL>", "created_at": "2025-07-12T21:50:58.071977", "last_accessed": "2025-07-12T21:51:01.692816", "resume_data": {"filename": "test_resume.pdf", "content": "dGVzdCBjb250ZW50", "content_type": "base64"}, "conversation_history": [{"timestamp": "2025-07-12T21:51:01.643787", "role": "user", "message": "What can you help me with?"}, {"timestamp": "2025-07-12T21:51:01.692816", "role": "manager", "message": "Hi Test User! I'm your Resume Intelligence Assistant. Please upload your resume first, and then I can help you with job recommendations, resume improvements, and more!", "action": "general_help"}], "improvement_suggestions": [], "job_recommendations": [], "context": {"email_provided": true, "resume_parsed": false, "jobs_searched": false}}, "30d64279-10e3-4286-8e05-1d06330391fe": {"user_name": "<PERSON>", "email": "<EMAIL>", "created_at": "2025-07-12T21:51:01.693331", "last_accessed": "2025-07-12T21:51:03.906195", "resume_data": {"filename": "test_resume.txt", "content": "********************************************************************************************", "content_type": "base64", "parsed": {}}, "conversation_history": [{"timestamp": "2025-07-12T21:51:03.906195", "role": "system", "message": "Resume uploaded and processed for <PERSON>"}], "improvement_suggestions": [], "job_recommendations": [], "context": {"email_provided": true, "resume_parsed": true, "jobs_searched": true}}, "30ce8405-d75f-40e5-8b57-6cabef82cee0": {"user_name": "Test User", "email": "<EMAIL>", "created_at": "2025-07-12T22:03:08.284208", "last_accessed": "2025-07-12T22:03:11.715369", "resume_data": {"filename": "test_resume.pdf", "content": "dGVzdCBjb250ZW50", "content_type": "base64"}, "conversation_history": [{"timestamp": "2025-07-12T22:03:11.669292", "role": "user", "message": "What can you help me with?"}, {"timestamp": "2025-07-12T22:03:11.715369", "role": "manager", "message": "Hi Test User! I'm your Resume Intelligence Assistant. Please upload your resume first, and then I can help you with job recommendations, resume improvements, and more!", "action": "general_help"}], "improvement_suggestions": [], "job_recommendations": [], "context": {"email_provided": true, "resume_parsed": false, "jobs_searched": false}}, "44399a91-2ea8-47f4-b2e8-7ef55ccd6d42": {"user_name": "<PERSON>", "email": "<EMAIL>", "created_at": "2025-07-12T22:03:11.721429", "last_accessed": "2025-07-12T22:03:14.402829", "resume_data": {"filename": "test_resume.txt", "content": "********************************************************************************************", "content_type": "base64", "parsed": {}}, "conversation_history": [{"timestamp": "2025-07-12T22:03:14.402829", "role": "system", "message": "Resume uploaded and processed for <PERSON>"}], "improvement_suggestions": [], "job_recommendations": [], "context": {"email_provided": true, "resume_parsed": true, "jobs_searched": true}}}