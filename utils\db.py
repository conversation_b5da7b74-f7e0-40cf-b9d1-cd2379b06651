import json
import os
import uuid
from datetime import datetime
from typing import Dict, Optional, Any

SESSIONS_FILE = "db/sessions.json"
USER_SESSIONS_FILE = "db/user_sessions.json"
os.makedirs("db", exist_ok=True)

class SessionManager:
    def __init__(self):
        self.sessions_file = SESSIONS_FILE
        self.user_sessions_file = USER_SESSIONS_FILE

    def load_sessions(self) -> Dict:
        """Load all sessions from JSON file"""
        if not os.path.exists(self.sessions_file):
            return {}
        try:
            with open(self.sessions_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}

    def save_sessions(self, sessions: Dict) -> None:
        """Save sessions to JSON file"""
        with open(self.sessions_file, "w", encoding="utf-8") as f:
            json.dump(sessions, f, indent=2, ensure_ascii=False)

    def load_user_sessions(self) -> Dict:
        """Load user name to session ID mapping"""
        if not os.path.exists(self.user_sessions_file):
            return {}
        try:
            with open(self.user_sessions_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}

    def save_user_sessions(self, user_sessions: Dict) -> None:
        """Save user name to session ID mapping"""
        with open(self.user_sessions_file, "w", encoding="utf-8") as f:
            json.dump(user_sessions, f, indent=2, ensure_ascii=False)

    def create_session(self, user_name: str, email: str = None, resume_data: Dict = None) -> str:
        """Create a new session for a user"""
        session_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()

        sessions = self.load_sessions()
        user_sessions = self.load_user_sessions()

        # Create session data
        session_data = {
            "user_name": user_name,
            "email": email,
            "created_at": current_time,
            "last_accessed": current_time,
            "resume_data": resume_data or {},
            "conversation_history": [],
            "improvement_suggestions": [],
            "job_recommendations": [],
            "context": {
                "email_provided": email is not None,
                "resume_parsed": resume_data is not None,
                "jobs_searched": False
            }
        }

        sessions[session_id] = session_data

        # Map user name to session ID (handle multiple sessions per user)
        if user_name not in user_sessions:
            user_sessions[user_name] = []
        user_sessions[user_name].append(session_id)

        self.save_sessions(sessions)
        self.save_user_sessions(user_sessions)

        return session_id

    def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session by session ID"""
        sessions = self.load_sessions()
        session = sessions.get(session_id)

        if session:
            # Update last accessed time
            session["last_accessed"] = datetime.now().isoformat()
            sessions[session_id] = session
            self.save_sessions(sessions)

        return session

    def get_session_by_name(self, user_name: str) -> Optional[Dict]:
        """Get the most recent session for a user by name"""
        user_sessions = self.load_user_sessions()

        if user_name not in user_sessions or not user_sessions[user_name]:
            return None

        # Get the most recent session ID
        session_ids = user_sessions[user_name]
        sessions = self.load_sessions()

        # Find the most recently accessed session
        most_recent_session = None
        most_recent_time = None

        for session_id in session_ids:
            if session_id in sessions:
                session = sessions[session_id]
                last_accessed = session.get("last_accessed")
                if not most_recent_time or last_accessed > most_recent_time:
                    most_recent_time = last_accessed
                    most_recent_session = session
                    most_recent_session["session_id"] = session_id

        return most_recent_session

    def update_session(self, session_id: str, updates: Dict) -> bool:
        """Update session data"""
        sessions = self.load_sessions()

        if session_id not in sessions:
            return False

        # Update the session
        sessions[session_id].update(updates)
        sessions[session_id]["last_accessed"] = datetime.now().isoformat()

        self.save_sessions(sessions)
        return True

    def add_to_conversation_history(self, session_id: str, message: Dict) -> bool:
        """Add a message to conversation history"""
        sessions = self.load_sessions()

        if session_id not in sessions:
            return False

        sessions[session_id]["conversation_history"].append({
            "timestamp": datetime.now().isoformat(),
            **message
        })
        sessions[session_id]["last_accessed"] = datetime.now().isoformat()

        self.save_sessions(sessions)
        return True

    def get_user_context(self, session_id: str) -> Dict:
        """Get user context for maintaining conversation state"""
        session = self.get_session(session_id)
        if not session:
            return {}

        return {
            "user_name": session.get("user_name"),
            "email": session.get("email"),
            "has_resume": bool(session.get("resume_data", {}).get("parsed")),
            "context": session.get("context", {}),
            "conversation_history": session.get("conversation_history", [])[-5:]  # Last 5 messages
        }

# Global session manager instance
session_manager = SessionManager()

# Legacy functions for backward compatibility
def load_sessions():
    return session_manager.load_sessions()

def save_sessions(sessions):
    session_manager.save_sessions(sessions)

def create_session(parsed_data, improvement):
    # Legacy function - create session with old format
    session_id = str(uuid.uuid4())
    sessions = load_sessions()
    sessions[session_id] = {
        "parsed": parsed_data,
        "improvement": improvement
    }
    save_sessions(sessions)
    return session_id

def get_session(session_id):
    return session_manager.get_session(session_id)
