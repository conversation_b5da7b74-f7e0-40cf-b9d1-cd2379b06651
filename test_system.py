#!/usr/bin/env python3
"""
Test script for the Resume Intelligence Assistant system
"""

import requests
import json
import time

# Configuration
API_BASE_URL = "http://localhost:8000"

def test_api_health():
    """Test if the API is running"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("✅ API is healthy")
            return True
        else:
            print("❌ API health check failed")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def test_session_management():
    """Test session management functionality"""
    print("\n🧪 Testing Session Management...")
    
    # Test creating a session (simulated)
    from utils.db import session_manager
    
    # Create a test session
    session_id = session_manager.create_session(
        user_name="Test User",
        email="<EMAIL>",
        resume_data={"filename": "test_resume.pdf", "content": b"test content"}
    )
    
    print(f"✅ Created session: {session_id}")
    
    # Test retrieving session
    session = session_manager.get_session(session_id)
    if session:
        print("✅ Session retrieval successful")
    else:
        print("❌ Session retrieval failed")
    
    # Test retrieving by name
    session_by_name = session_manager.get_session_by_name("Test User")
    if session_by_name:
        print("✅ Session retrieval by name successful")
    else:
        print("❌ Session retrieval by name failed")
    
    return session_id

def test_manager_agent():
    """Test manager agent functionality"""
    print("\n🧪 Testing Manager Agent...")
    
    from agents.manager import manager_agent
    
    # Test processing user request
    result = manager_agent.process_user_request(
        user_input="What can you help me with?",
        user_name="Test User"
    )
    
    if "message" in result:
        print("✅ Manager agent response successful")
        print(f"Response: {result['message']}")
    else:
        print("❌ Manager agent failed")
        print(f"Error: {result}")

def test_chat_endpoint():
    """Test the chat endpoint"""
    print("\n🧪 Testing Chat Endpoint...")
    
    if not test_api_health():
        print("❌ Skipping chat test - API not available")
        return
    
    # Test chat request
    chat_data = {
        "user_input": "Hello, what can you help me with?",
        "user_name": "Test User"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/chat", json=chat_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ Chat endpoint successful")
            print(f"Response: {result.get('message', 'No message')}")
        else:
            print(f"❌ Chat endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Chat endpoint error: {e}")

def test_file_upload_simulation():
    """Simulate file upload test"""
    print("\n🧪 Testing File Upload Simulation...")
    
    # Create a dummy file content
    dummy_content = b"John Doe\nSoftware Engineer\<EMAIL>\nPython, JavaScript, React"
    
    from agents.manager import manager_agent
    
    result = manager_agent.process_resume_upload(
        file_name="test_resume.txt",
        file_content=dummy_content,
        user_name="John Doe",
        email="<EMAIL>"
    )
    
    if "session_id" in result:
        print("✅ Resume upload simulation successful")
        print(f"Session ID: {result['session_id']}")
        return result['session_id']
    else:
        print("❌ Resume upload simulation failed")
        print(f"Error: {result}")
        return None

def main():
    """Run all tests"""
    print("🚀 Starting Resume Intelligence Assistant System Tests\n")
    
    # Test 1: Session Management
    session_id = test_session_management()
    
    # Test 2: Manager Agent
    test_manager_agent()
    
    # Test 3: File Upload Simulation
    upload_session_id = test_file_upload_simulation()
    
    # Test 4: API Health (if running)
    test_api_health()
    
    # Test 5: Chat Endpoint (if API is running)
    test_chat_endpoint()
    
    print("\n✅ All tests completed!")
    print("\n📋 Next Steps:")
    print("1. Start the FastAPI server: python -m uvicorn main:app --reload")
    print("2. Start the Streamlit app: streamlit run app.py")
    print("3. Test the full system with real resume uploads")

if __name__ == "__main__":
    main()
