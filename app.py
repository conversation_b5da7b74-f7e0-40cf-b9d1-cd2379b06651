import streamlit as st
import requests
import json
from datetime import datetime

# Configure page
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

st.title("🤖 Resume Intelligence Assistant")
st.markdown("*Your AI-powered career companion*")

# Initialize session state
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'user_name' not in st.session_state:
    st.session_state.user_name = None
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []

# Sidebar for session management
with st.sidebar:
    st.header("Session Management")

    mode = st.radio("Choose Mode", ["New User", "Returning User", "Chat with Assistant"])

    if mode == "New User":
        st.subheader("Upload Your Resume")
        user_name = st.text_input("Your Name", placeholder="e.g., <PERSON>")
        email = st.text_input("Email (Optional)", placeholder="<EMAIL>")
        uploaded_file = st.file_uploader("Upload Resume", type=["pdf", "docx"])

        if st.button("Process Resume") and uploaded_file and user_name:
            with st.spinner("Processing your resume..."):
                try:
                    files = {"file": (uploaded_file.name, uploaded_file.getvalue())}
                    data = {"user_name": user_name}
                    if email:
                        data["email"] = email

                    response = requests.post(
                        "http://localhost:8000/upload_resume",
                        files=files,
                        data=data
                    )

                    if response.status_code == 200:
                        result = response.json()
                        st.session_state.session_id = result["session_id"]
                        st.session_state.user_name = user_name
                        st.success(f"Welcome {user_name}! Your resume has been processed.")
                        st.info(f"Session ID: {result['session_id']}")
                        st.rerun()
                    else:
                        st.error(f"Error: {response.json().get('detail', 'Unknown error')}")

                except Exception as e:
                    st.error(f"Error processing resume: {str(e)}")

    elif mode == "Returning User":
        st.subheader("Access Your Session")

        access_method = st.radio("Access by:", ["Name", "Session ID"])

        if access_method == "Name":
            user_name_input = st.text_input("Enter your name", placeholder="e.g., John Doe")
            if st.button("Load Session") and user_name_input:
                with st.spinner("Loading your session..."):
                    try:
                        response = requests.get(f"http://localhost:8000/user/{user_name_input}/session")
                        if response.status_code == 200:
                            result = response.json()
                            st.session_state.session_id = result["session_id"]
                            st.session_state.user_name = result["user_name"]
                            st.success(f"Welcome back, {result['user_name']}!")
                            st.info(f"Session ID: {result['session_id']}")
                            st.rerun()
                        else:
                            st.error(f"No session found for '{user_name_input}'. Please upload a resume first.")
                    except Exception as e:
                        st.error(f"Error loading session: {str(e)}")

        else:  # Session ID
            session_id_input = st.text_input("Enter Session ID")
            if st.button("Load Session") and session_id_input:
                with st.spinner("Loading your session..."):
                    try:
                        response = requests.get(f"http://localhost:8000/session/{session_id_input}")
                        if response.status_code == 200:
                            result = response.json()
                            st.session_state.session_id = session_id_input
                            st.session_state.user_name = result["user_name"]
                            st.success(f"Welcome back, {result['user_name']}!")
                            st.rerun()
                        else:
                            st.error("Session not found.")
                    except Exception as e:
                        st.error(f"Error loading session: {str(e)}")

    # Show current session info
    if st.session_state.session_id:
        st.success(f"✅ Active Session")
        st.write(f"**User:** {st.session_state.user_name}")
        st.write(f"**Session ID:** {st.session_state.session_id[:8]}...")

        if st.button("Clear Session"):
            st.session_state.session_id = None
            st.session_state.user_name = None
            st.session_state.chat_history = []
            st.rerun()

# Main content area
if not st.session_state.session_id:
    st.info("👈 Please start by uploading your resume or accessing an existing session from the sidebar.")

    # Show demo information
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Resume Analysis")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

    with col2:
        st.subheader("🛠 Improvement Suggestions")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

    with col3:
        st.subheader("💼 Job Recommendations")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # Chat interface for active session
    if mode == "Chat with Assistant" or st.session_state.session_id:
        st.header(f"💬 Chat with Assistant - {st.session_state.user_name}")

        # Quick action buttons
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if st.button("🔍 Find Jobs"):
                st.session_state.chat_history.append({"role": "user", "message": "Find job recommendations for me"})

        with col2:
            if st.button("📈 Improve Resume"):
                st.session_state.chat_history.append({"role": "user", "message": "Give me suggestions to improve my resume"})

        with col3:
            if st.button("📊 Show Resume Data"):
                st.session_state.chat_history.append({"role": "user", "message": "Show me my parsed resume data"})

        with col4:
            if st.button("❓ Help"):
                st.session_state.chat_history.append({"role": "user", "message": "What can you help me with?"})

        # Chat input
        user_input = st.chat_input("Ask me anything about your resume, jobs, or career advice...")

        if user_input:
            st.session_state.chat_history.append({"role": "user", "message": user_input})

        # Process the latest message if there's a new one
        if st.session_state.chat_history and st.session_state.chat_history[-1]["role"] == "user":
            latest_message = st.session_state.chat_history[-1]["message"]

            with st.spinner("Assistant is thinking..."):
                try:
                    response = requests.post(
                        "http://localhost:8000/chat",
                        json={
                            "user_input": latest_message,
                            "session_id": st.session_state.session_id
                        }
                    )

                    if response.status_code == 200:
                        result = response.json()
                        assistant_message = result["message"]

                        # Add assistant response to chat history
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "message": assistant_message,
                            "data": result.get("data")
                        })

                    else:
                        st.error("Error communicating with assistant")

                except Exception as e:
                    st.error(f"Error: {str(e)}")

        # Display chat history
        for i, chat in enumerate(st.session_state.chat_history):
            if chat["role"] == "user":
                with st.chat_message("user"):
                    st.write(chat["message"])
            else:
                with st.chat_message("assistant"):
                    st.write(chat["message"])

                    # Display additional data if available
                    if "data" in chat and chat["data"]:
                        data = chat["data"]

                        if data.get("parsed"):
                            with st.expander("📄 Parsed Resume Data"):
                                st.json(data["parsed"])

                        if data.get("improvements"):
                            with st.expander("🛠 Improvement Suggestions"):
                                for improvement in data["improvements"]:
                                    st.write(f"• {improvement}")

                        if data.get("jobs"):
                            with st.expander("💼 Job Recommendations"):
                                for job in data["jobs"]:
                                    if isinstance(job, dict) and "title" in job:
                                        if "url" in job:
                                            st.markdown(f"[{job['title']}]({job['url']})")
                                        else:
                                            st.write(f"• {job['title']}")
                                    else:
                                        st.write(f"• {job}")

        # Session data viewer
        with st.expander("📊 View Full Session Data"):
            if st.button("Load Session Data"):
                try:
                    response = requests.get(f"http://localhost:8000/session/{st.session_state.session_id}")
                    if response.status_code == 200:
                        session_data = response.json()

                        col1, col2 = st.columns(2)

                        with col1:
                            st.subheader("📄 Resume Information")
                            st.json(session_data.get("parsed", {}))

                            st.subheader("🛠 Improvement Suggestions")
                            for suggestion in session_data.get("improvement_suggestions", []):
                                st.write(f"• {suggestion}")

                        with col2:
                            st.subheader("💼 Job Recommendations")
                            for job in session_data.get("job_recommendations", []):
                                if isinstance(job, dict) and "title" in job:
                                    if "url" in job:
                                        st.markdown(f"[{job['title']}]({job['url']})")
                                    else:
                                        st.write(f"• {job['title']}")
                                else:
                                    st.write(f"• {job}")

                            st.subheader("📈 Session Statistics")
                            st.write(f"**Created:** {session_data.get('created_at', 'N/A')}")
                            st.write(f"**Last Accessed:** {session_data.get('last_accessed', 'N/A')}")
                            st.write(f"**Conversations:** {len(session_data.get('conversation_history', []))}")

                except Exception as e:
                    st.error(f"Error loading session data: {str(e)}")

# Footer
st.markdown("---")
st.markdown("*Resume Intelligence Assistant - Powered by AI*")

