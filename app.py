import streamlit as st
import requests

# Configure page
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

# Initialize session state
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'user_info' not in st.session_state:
    st.session_state.user_info = None
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []

# API Configuration
API_BASE_URL = "http://localhost:8000"

def make_authenticated_request(method, endpoint, **kwargs):
    """Make an authenticated request to the API"""
    headers = kwargs.get('headers', {})
    if st.session_state.access_token:
        headers['Authorization'] = f"Bearer {st.session_state.access_token}"
    kwargs['headers'] = headers
    
    if method.upper() == 'GET':
        return requests.get(f"{API_BASE_URL}{endpoint}", **kwargs)
    elif method.upper() == 'POST':
        return requests.post(f"{API_BASE_URL}{endpoint}", **kwargs)
    elif method.upper() == 'PUT':
        return requests.put(f"{API_BASE_URL}{endpoint}", **kwargs)
    elif method.upper() == 'DELETE':
        return requests.delete(f"{API_BASE_URL}{endpoint}", **kwargs)

def logout():
    """Clear session and logout user"""
    st.session_state.access_token = None
    st.session_state.user_info = None
    st.session_state.session_id = None
    st.session_state.chat_history = []
    st.rerun()

# Check if user is authenticated
if not st.session_state.access_token:
    # Show login/signup page
    st.title("🤖 Resume Intelligence Assistant")
    st.markdown("*Your AI-powered career companion*")
    
    tab1, tab2 = st.tabs(["Login", "Sign Up"])
    
    with tab1:
        st.subheader("Login to Your Account")
        
        with st.form("login_form"):
            email = st.text_input("Email", placeholder="<EMAIL>")
            password = st.text_input("Password", type="password")
            login_button = st.form_submit_button("Login")
            
            if login_button and email and password:
                with st.spinner("Logging in..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/auth/login",
                            json={"email": email, "password": password}
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            st.session_state.access_token = result["access_token"]
                            st.session_state.user_info = result["user"]
                            st.success(f"Welcome back, {result['user']['full_name']}!")
                            st.rerun()
                        else:
                            error_detail = response.json().get("detail", "Login failed")
                            st.error(f"Login failed: {error_detail}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")
    
    with tab2:
        st.subheader("Create New Account")
        
        with st.form("signup_form"):
            full_name = st.text_input("Full Name", placeholder="John Doe")
            email = st.text_input("Email", placeholder="<EMAIL>")
            password = st.text_input("Password", type="password")
            confirm_password = st.text_input("Confirm Password", type="password")
            signup_button = st.form_submit_button("Sign Up")
            
            if signup_button and email and password and full_name:
                if password != confirm_password:
                    st.error("Passwords do not match!")
                elif len(password) < 6:
                    st.error("Password must be at least 6 characters long!")
                else:
                    with st.spinner("Creating account..."):
                        try:
                            response = requests.post(
                                f"{API_BASE_URL}/auth/signup",
                                json={
                                    "email": email,
                                    "password": password,
                                    "full_name": full_name
                                }
                            )
                            
                            if response.status_code == 200:
                                result = response.json()
                                st.session_state.access_token = result["access_token"]
                                st.session_state.user_info = result["user"]
                                st.success(f"Account created! Welcome, {result['user']['full_name']}!")
                                st.rerun()
                            else:
                                error_detail = response.json().get("detail", "Signup failed")
                                st.error(f"Signup failed: {error_detail}")
                        except Exception as e:
                            st.error(f"Error: {str(e)}")
    
    # Show demo information
    st.markdown("---")
    st.subheader("✨ Features")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("**📄 Resume Analysis**")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")
    
    with col2:
        st.markdown("**🛠 Improvement Suggestions**")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")
    
    with col3:
        st.markdown("**💼 Job Recommendations**")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # User is authenticated - show main app
    st.title("🤖 Resume Intelligence Assistant")
    st.markdown(f"*Welcome back, {st.session_state.user_info['full_name']}!*")
    
    # Header with user info and logout
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("Logout"):
            logout()

    # Sidebar for actions
    with st.sidebar:
        st.header("Actions")
        
        # Upload Resume
        st.subheader("📄 Upload Resume")
        uploaded_file = st.file_uploader("Upload Resume", type=["pdf", "docx"])
        
        if st.button("Process Resume") and uploaded_file:
            with st.spinner("Processing your resume..."):
                try:
                    files = {"file": (uploaded_file.name, uploaded_file.getvalue())}
                    
                    response = make_authenticated_request(
                        "POST", "/upload_resume",
                        files=files
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        st.session_state.session_id = result["session_id"]
                        
                        st.success(f"Resume processed successfully!")
                        st.info(f"Session ID: {result['session_id']}")
                        
                        # Show extracted information
                        if result.get("parsed"):
                            with st.expander("📄 Extracted Information"):
                                parsed_data = result["parsed"]
                                if parsed_data.get("name"):
                                    st.write(f"**Name:** {parsed_data['name']}")
                                if parsed_data.get("email"):
                                    st.write(f"**Email:** {parsed_data['email']}")
                                if parsed_data.get("phone"):
                                    st.write(f"**Phone:** {parsed_data['phone']}")
                                if parsed_data.get("Technical Skills"):
                                    st.write(f"**Skills:** {', '.join(parsed_data['Technical Skills'])}")
                        
                        st.rerun()
                    else:
                        st.error(f"Error: {response.json().get('detail', 'Unknown error')}")
                        
                except Exception as e:
                    st.error(f"Error processing resume: {str(e)}")
        
        # Show current session info
        if st.session_state.session_id:
            st.success(f"✅ Active Session")
            st.write(f"**Session ID:** {st.session_state.session_id[:8]}...")
        
        # User info
        st.markdown("---")
        st.subheader("👤 User Info")
        st.write(f"**Name:** {st.session_state.user_info['full_name']}")
        st.write(f"**Email:** {st.session_state.user_info['email']}")
    
    # Main content area
    if not st.session_state.session_id:
        st.info("👈 Please upload your resume from the sidebar to get started.")
        
        # Show demo information
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.subheader("📄 Resume Analysis")
            st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")
        
        with col2:
            st.subheader("🛠 Improvement Suggestions")
            st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")
        
        with col3:
            st.subheader("💼 Job Recommendations")
            st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")
    
    else:
        # Chat interface for active session
        st.header(f"💬 Chat with Assistant")
        
        # Quick action buttons
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("🔍 Find Jobs"):
                st.session_state.chat_history.append({"role": "user", "message": "Find job recommendations for me"})
        
        with col2:
            if st.button("📈 Improve Resume"):
                st.session_state.chat_history.append({"role": "user", "message": "Give me suggestions to improve my resume"})
        
        with col3:
            if st.button("📊 Show Resume Data"):
                st.session_state.chat_history.append({"role": "user", "message": "Show me my parsed resume data"})
        
        with col4:
            if st.button("❓ Help"):
                st.session_state.chat_history.append({"role": "user", "message": "What can you help me with?"})
        
        # Chat input
        user_input = st.chat_input("Ask me anything about your resume, jobs, or career advice...")
        
        if user_input:
            st.session_state.chat_history.append({"role": "user", "message": user_input})
        
        # Process the latest message if there's a new one
        if st.session_state.chat_history and st.session_state.chat_history[-1]["role"] == "user":
            latest_message = st.session_state.chat_history[-1]["message"]
            
            with st.spinner("Assistant is thinking..."):
                try:
                    response = make_authenticated_request(
                        "POST", "/chat",
                        json={
                            "user_input": latest_message,
                            "session_id": st.session_state.session_id
                        }
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        assistant_message = result["message"]
                        
                        # Add assistant response to chat history
                        st.session_state.chat_history.append({
                            "role": "assistant", 
                            "message": assistant_message,
                            "data": result.get("data")
                        })
                        
                    else:
                        st.error("Error communicating with assistant")
                        
                except Exception as e:
                    st.error(f"Error: {str(e)}")
        
        # Display chat history
        for i, chat in enumerate(st.session_state.chat_history):
            if chat["role"] == "user":
                with st.chat_message("user"):
                    st.write(chat["message"])
            else:
                with st.chat_message("assistant"):
                    st.write(chat["message"])
                    
                    # Display additional data if available
                    if "data" in chat and chat["data"]:
                        data = chat["data"]
                        
                        if data.get("parsed"):
                            with st.expander("📄 Parsed Resume Data"):
                                st.json(data["parsed"])
                        
                        if data.get("improvements"):
                            with st.expander("🛠 Improvement Suggestions"):
                                for improvement in data["improvements"]:
                                    st.write(f"• {improvement}")
                        
                        if data.get("jobs"):
                            with st.expander("💼 Job Recommendations"):
                                for job in data["jobs"]:
                                    if isinstance(job, dict) and "title" in job:
                                        if "url" in job:
                                            st.markdown(f"[{job['title']}]({job['url']})")
                                        else:
                                            st.write(f"• {job['title']}")
                                    else:
                                        st.write(f"• {job}")

    # Footer
    st.markdown("---")
    st.markdown("*Resume Intelligence Assistant - Powered by AI with JWT Authentication*")
