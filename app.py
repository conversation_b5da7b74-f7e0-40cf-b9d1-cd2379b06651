import streamlit as st
import requests
import uuid
import json

# Page configuration
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

# API Configuration
API_BASE_URL = "http://localhost:8000"

# Helper functions
def make_api_request(method, endpoint, **kwargs):
    """Make API request without authentication"""
    try:
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30

        if method.upper() == 'POST':
            response = requests.post(f"{API_BASE_URL}{endpoint}", **kwargs)
        elif method.upper() == 'GET':
            response = requests.get(f"{API_BASE_URL}{endpoint}", **kwargs)
        else:
            return None

        return response
    except Exception as e:
        st.error(f"API Error: {str(e)}")
        return None

def safe_json_response(response):
    """Safely extract JSON from response"""
    if response is None:
        return None
    try:
        return response.json()
    except:
        return None

# Initialize session state
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'resume_data' not in st.session_state:
    st.session_state.resume_data = None

# Main app without authentication
st.title("🤖 Resume Intelligence Assistant")
st.markdown("*Your AI-powered career companion - No login required!*")

# Sidebar for file upload
with st.sidebar:
    st.header("📄 Upload Resume")
    uploaded_file = st.file_uploader(
        "Choose your resume file",
        type=['pdf', 'docx'],
        help="Upload your resume in PDF or DOCX format"
    )

    if st.button("Process Resume") and uploaded_file:
        with st.spinner("Processing your resume..."):
            try:
                # Prepare file for upload
                files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}

                # Try to call the API first
                response = make_api_request("POST", "/upload_resume_simple", files=files)

                if response and hasattr(response, 'status_code') and response.status_code == 200:
                    # API call successful
                    result = safe_json_response(response)
                    if result and result.get("session_id"):
                        st.session_state.session_id = result["session_id"]
                        st.session_state.resume_data = result.get("parsed", {})

                        st.success("Resume processed successfully!")
                        st.info(f"Session ID: {result['session_id'][:8]}...")

                        # Show extracted information
                        if result.get("parsed"):
                            with st.expander("📄 Extracted Information"):
                                parsed_data = result["parsed"]
                                if parsed_data.get("name"):
                                    st.write(f"**Name:** {parsed_data['name']}")
                                if parsed_data.get("email"):
                                    st.write(f"**Email:** {parsed_data['email']}")
                                if parsed_data.get("phone"):
                                    st.write(f"**Phone:** {parsed_data['phone']}")
                                if parsed_data.get("Technical Skills"):
                                    st.write(f"**Skills:** {', '.join(parsed_data['Technical Skills'])}")

                        st.rerun()
                    else:
                        st.error("Invalid response from server")
                else:
                    # API failed, create demo session
                    st.warning("⚠️ API not available, running in demo mode")
                    st.session_state.session_id = str(uuid.uuid4())
                    st.session_state.resume_data = {
                        "name": "Demo User",
                        "email": "<EMAIL>",
                        "phone": "******-0123",
                        "Technical Skills": ["Python", "JavaScript", "React", "Node.js", "AWS"],
                        "experience": ["Software Engineer at TechCorp", "Junior Developer at StartupXYZ"],
                        "education": ["BS Computer Science - University of Technology"]
                    }
                    st.success("Resume processed in demo mode!")
                    st.info(f"Demo Session ID: {st.session_state.session_id[:8]}...")
                    st.rerun()

            except Exception as e:
                st.error(f"Error processing resume: {str(e)}")
                # Fallback to demo mode
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.resume_data = {"name": "Demo User", "Technical Skills": ["Python", "JavaScript"]}
                st.info("Running in demo mode due to error")
                st.rerun()

    # Show current session info
    if st.session_state.session_id:
        st.success(f"✅ Active Session")
        st.write(f"ID: {st.session_state.session_id[:8]}...")
        if st.session_state.resume_data and st.session_state.resume_data.get('name'):
            st.write(f"**User:** {st.session_state.resume_data['name']}")

    st.markdown("---")
    st.subheader("👤 Session Info")
    st.write("**Status:** No login required")
    st.write("**Mode:** Resume processing enabled")

# Main content area
if not st.session_state.session_id:
    st.info("👈 Please upload your resume from the sidebar to get started.")

    # Show demo information
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Resume Analysis")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

    with col2:
        st.subheader("🛠 Improvement Suggestions")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

    with col3:
        st.subheader("💼 Job Recommendations")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # Action buttons interface for active session
    st.header(f"🤖 Resume Intelligence Assistant")
    st.subheader("What would you like me to help you with?")

    # Action buttons in a grid
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔍 Find Job Recommendations", use_container_width=True):
            with st.spinner("Finding job recommendations..."):
                # Demo job recommendations
                st.subheader("💼 Job Recommendations")
                st.info("Found 12 relevant job opportunities")

                demo_jobs = [
                    {"title": "Senior Python Developer", "company": "Google", "source": "LinkedIn", "relevance_score": 95},
                    {"title": "Full Stack Engineer", "company": "Microsoft", "source": "Indeed", "relevance_score": 90},
                    {"title": "Software Engineer", "company": "Amazon", "source": "Glassdoor", "relevance_score": 88},
                    {"title": "Backend Developer", "company": "Meta", "source": "Company Website", "relevance_score": 85},
                    {"title": "DevOps Engineer", "company": "Netflix", "source": "Remote.co", "relevance_score": 82}
                ]

                for i, job in enumerate(demo_jobs, 1):
                    with st.container():
                        col1, col2 = st.columns([3, 1])

                        with col1:
                            st.markdown(f"**{i}. {job['title']}**")
                            st.write(f"🏢 {job['company']} | 📍 {job['source']}")
                            st.write(f"📝 Exciting opportunity to work with cutting-edge technologies...")

                        with col2:
                            score = job['relevance_score']
                            color = "green" if score >= 80 else "orange" if score >= 60 else "red"
                            st.markdown(f"<div style='text-align: center; color: {color}; font-weight: bold;'>Match: {score}%</div>", unsafe_allow_html=True)

                        st.markdown("---")

        if st.button("📊 Show Resume Data", use_container_width=True):
            with st.spinner("Loading resume data..."):
                st.subheader("📄 Your Resume Data")
                if st.session_state.resume_data:
                    st.json(st.session_state.resume_data)
                else:
                    st.warning("No resume data available. Please upload and process a resume first.")

    with col2:
        if st.button("📈 Improve My Resume", use_container_width=True):
            with st.spinner("Analyzing your resume..."):
                st.subheader("🛠 Improvement Suggestions")
                improvements = [
                    "Add more quantifiable achievements with specific metrics",
                    "Include relevant certifications (AWS, Google Cloud)",
                    "Expand the technical skills section with frameworks",
                    "Add a professional summary at the top",
                    "Include links to GitHub and portfolio projects"
                ]
                for improvement in improvements:
                    st.write(f"• {improvement}")

        if st.button("❓ Get Help", use_container_width=True):
            with st.spinner("Loading help information..."):
                st.info("🚀 Welcome to Resume Intelligence Assistant! I can help you with:\n\n🔍 **Job Recommendations** - Find relevant positions based on your skills\n\n📈 **Resume Improvements** - Get suggestions to enhance your resume\n\n📊 **Resume Analysis** - View parsed data from your uploaded resume\n\nSimply click any of the action buttons to get started!")

    # Show session statistics
    st.markdown("---")
    st.subheader("📊 Session Information")
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**Mode:** No Authentication Required")
        st.write(f"**Session:** {st.session_state.session_id[:8]}...")
    with col2:
        st.write(f"**Status:** Resume Uploaded ✅")
        if st.session_state.resume_data and st.session_state.resume_data.get('name'):
            st.write(f"**User:** {st.session_state.resume_data['name']}")
        else:
            st.write(f"**Data:** Processing enabled")

# Footer
st.markdown("---")
st.markdown("*Resume Intelligence Assistant - Powered by AI (Demo Mode - No Authentication Required)*")
