import streamlit as st
import requests
import uuid

# Page configuration
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

# Initialize session state
if 'demo_session_id' not in st.session_state:
    st.session_state.demo_session_id = None

# Main app without authentication
st.title("🤖 Resume Intelligence Assistant")
st.markdown("*Your AI-powered career companion - No login required!*")

# Sidebar for file upload
with st.sidebar:
    st.header("📄 Upload Resume")
    uploaded_file = st.file_uploader(
        "Choose your resume file",
        type=['pdf', 'docx'],
        help="Upload your resume in PDF or DOCX format"
    )

    if st.button("Process Resume") and uploaded_file:
        with st.spinner("Processing your resume..."):
            # Create a simple demo session
            st.session_state.demo_session_id = str(uuid.uuid4())
            st.success("Resume processed successfully!")
            st.info(f"Demo Session ID: {st.session_state.demo_session_id[:8]}...")
            st.rerun()

    # Show current session info
    if st.session_state.demo_session_id:
        st.success(f"✅ Active Demo Session")
        st.write(f"ID: {st.session_state.demo_session_id[:8]}...")
    
    st.markdown("---")
    st.subheader("👤 Demo Mode")
    st.write("**Status:** No login required")
    st.write("**Mode:** Public demo")

# Main content area
if not st.session_state.demo_session_id:
    st.info("👈 Please upload your resume from the sidebar to get started.")

    # Show demo information
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Resume Analysis")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

    with col2:
        st.subheader("🛠 Improvement Suggestions")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

    with col3:
        st.subheader("💼 Job Recommendations")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # Action buttons interface for active session
    st.header(f"🤖 Resume Intelligence Assistant")
    st.subheader("What would you like me to help you with?")

    # Action buttons in a grid
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔍 Find Job Recommendations", use_container_width=True):
            with st.spinner("Finding job recommendations..."):
                # Demo job recommendations
                st.subheader("💼 Job Recommendations")
                st.info("Found 12 relevant job opportunities")
                
                demo_jobs = [
                    {"title": "Senior Python Developer", "company": "Google", "source": "LinkedIn", "relevance_score": 95},
                    {"title": "Full Stack Engineer", "company": "Microsoft", "source": "Indeed", "relevance_score": 90},
                    {"title": "Software Engineer", "company": "Amazon", "source": "Glassdoor", "relevance_score": 88},
                    {"title": "Backend Developer", "company": "Meta", "source": "Company Website", "relevance_score": 85},
                    {"title": "DevOps Engineer", "company": "Netflix", "source": "Remote.co", "relevance_score": 82}
                ]
                
                for i, job in enumerate(demo_jobs, 1):
                    with st.container():
                        col1, col2 = st.columns([3, 1])
                        
                        with col1:
                            st.markdown(f"**{i}. {job['title']}**")
                            st.write(f"🏢 {job['company']} | 📍 {job['source']}")
                            st.write(f"📝 Exciting opportunity to work with cutting-edge technologies...")
                        
                        with col2:
                            score = job['relevance_score']
                            color = "green" if score >= 80 else "orange" if score >= 60 else "red"
                            st.markdown(f"<div style='text-align: center; color: {color}; font-weight: bold;'>Match: {score}%</div>", unsafe_allow_html=True)
                        
                        st.markdown("---")
        
        if st.button("📊 Show Resume Data", use_container_width=True):
            with st.spinner("Loading resume data..."):
                st.subheader("📄 Your Resume Data")
                demo_data = {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "phone": "******-0123",
                    "Technical Skills": ["Python", "JavaScript", "React", "Node.js", "AWS"],
                    "experience": ["Software Engineer at TechCorp", "Junior Developer at StartupXYZ"],
                    "education": ["BS Computer Science - University of Technology"]
                }
                st.json(demo_data)
    
    with col2:
        if st.button("📈 Improve My Resume", use_container_width=True):
            with st.spinner("Analyzing your resume..."):
                st.subheader("🛠 Improvement Suggestions")
                improvements = [
                    "Add more quantifiable achievements with specific metrics",
                    "Include relevant certifications (AWS, Google Cloud)",
                    "Expand the technical skills section with frameworks",
                    "Add a professional summary at the top",
                    "Include links to GitHub and portfolio projects"
                ]
                for improvement in improvements:
                    st.write(f"• {improvement}")
        
        if st.button("❓ Get Help", use_container_width=True):
            with st.spinner("Loading help information..."):
                st.info("🚀 Welcome to Resume Intelligence Assistant! I can help you with:\n\n🔍 **Job Recommendations** - Find relevant positions based on your skills\n\n📈 **Resume Improvements** - Get suggestions to enhance your resume\n\n📊 **Resume Analysis** - View parsed data from your uploaded resume\n\nSimply click any of the action buttons to get started!")

    # Show session statistics
    st.markdown("---")
    st.subheader("📊 Session Information")
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**Mode:** Demo (No Authentication)")
        st.write(f"**Session:** {st.session_state.demo_session_id[:8]}...")
    with col2:
        st.write(f"**Status:** Resume Uploaded ✅")
        st.write(f"**Features:** All Available")

# Footer
st.markdown("---")
st.markdown("*Resume Intelligence Assistant - Powered by AI (Demo Mode - No Authentication Required)*")
