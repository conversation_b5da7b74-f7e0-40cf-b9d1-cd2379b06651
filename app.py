import streamlit as st
import requests

# Configure page
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

# Initialize session state
if 'access_token' not in st.session_state:
    st.session_state.access_token = None
if 'user_info' not in st.session_state:
    st.session_state.user_info = None
if 'session_id' not in st.session_state:
    st.session_state.session_id = None

# API Configuration
API_BASE_URL = "http://localhost:8000"

def make_authenticated_request(method, endpoint, **kwargs):
    """Make an authenticated request to the API with robust error handling"""
    try:
        headers = kwargs.get('headers', {})
        if st.session_state.access_token:
            headers['Authorization'] = f"Bearer {st.session_state.access_token}"
        kwargs['headers'] = headers

        # Add timeout to prevent hanging
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30

        if method.upper() == 'GET':
            response = requests.get(f"{API_BASE_URL}{endpoint}", **kwargs)
        elif method.upper() == 'POST':
            response = requests.post(f"{API_BASE_URL}{endpoint}", **kwargs)
        elif method.upper() == 'PUT':
            response = requests.put(f"{API_BASE_URL}{endpoint}", **kwargs)
        elif method.upper() == 'DELETE':
            response = requests.delete(f"{API_BASE_URL}{endpoint}", **kwargs)
        else:
            st.error(f"Unsupported HTTP method: {method}")
            return None

        return response

    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to the server. Please make sure the API is running.")
        return None
    except requests.exceptions.Timeout:
        st.error("⏱️ Request timed out. Please try again.")
        return None
    except requests.exceptions.RequestException as e:
        st.error(f"❌ Request failed: {str(e)}")
        return None
    except Exception as e:
        st.error(f"❌ Unexpected error: {str(e)}")
        return None

def safe_json_response(response):
    """Safely extract JSON from response with error handling"""
    if response is None:
        return None

    try:
        return response.json()
    except ValueError:
        st.error("❌ Invalid response format from server")
        return None
    except Exception as e:
        st.error(f"❌ Error parsing response: {str(e)}")
        return None

def logout():
    """Clear session and logout user"""
    st.session_state.access_token = None
    st.session_state.user_info = None
    st.session_state.session_id = None
    st.rerun()

# Check if user is authenticated
if not st.session_state.access_token:
    # Show login/signup page
    st.title("🤖 Resume Intelligence Assistant")
    st.markdown("*Your AI-powered career companion*")

    tab1, tab2 = st.tabs(["Login", "Sign Up"])

    with tab1:
        st.subheader("Login to Your Account")

        with st.form("login_form"):
            email = st.text_input("Email", placeholder="<EMAIL>")
            password = st.text_input("Password", type="password")
            login_button = st.form_submit_button("Login")

            if login_button and email and password:
                with st.spinner("Logging in..."):
                    try:
                        response = requests.post(
                            f"{API_BASE_URL}/auth/login",
                            json={"email": email, "password": password}
                        )

                        if response and response.status_code == 200:
                            result = safe_json_response(response)
                            if result and result.get("access_token") and result.get("user"):
                                st.session_state.access_token = result["access_token"]
                                st.session_state.user_info = result["user"]
                                st.success(f"Welcome back, {result['user']['full_name']}!")
                                st.rerun()
                            else:
                                st.error("Invalid response from server")
                        else:
                            error_result = safe_json_response(response) if response else {}
                            error_detail = error_result.get("detail", "Login failed") if error_result else "Connection failed"
                            st.error(f"Login failed: {error_detail}")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

    with tab2:
        st.subheader("Create New Account")

        with st.form("signup_form"):
            full_name = st.text_input("Full Name", placeholder="John Doe")
            email = st.text_input("Email", placeholder="<EMAIL>")
            password = st.text_input("Password", type="password")
            confirm_password = st.text_input("Confirm Password", type="password")
            signup_button = st.form_submit_button("Sign Up")

            if signup_button and email and password and full_name:
                if password != confirm_password:
                    st.error("Passwords do not match!")
                elif len(password) < 6:
                    st.error("Password must be at least 6 characters long!")
                else:
                    with st.spinner("Creating account..."):
                        try:
                            response = requests.post(
                                f"{API_BASE_URL}/auth/signup",
                                json={
                                    "email": email,
                                    "password": password,
                                    "full_name": full_name
                                }
                            )

                            if response and response.status_code == 200:
                                result = safe_json_response(response)
                                if result and result.get("access_token") and result.get("user"):
                                    st.session_state.access_token = result["access_token"]
                                    st.session_state.user_info = result["user"]
                                    st.success(f"Account created! Welcome, {result['user']['full_name']}!")
                                    st.rerun()
                                else:
                                    st.error("Invalid response from server")
                            else:
                                error_result = safe_json_response(response) if response else {}
                                error_detail = error_result.get("detail", "Signup failed") if error_result else "Connection failed"
                                st.error(f"Signup failed: {error_detail}")
                        except Exception as e:
                            st.error(f"Error: {str(e)}")

    # Show demo information
    st.markdown("---")
    st.subheader("✨ Features")
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("**📄 Resume Analysis**")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

    with col2:
        st.markdown("**🛠 Improvement Suggestions**")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

    with col3:
        st.markdown("**💼 Job Recommendations**")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # User is authenticated - show main app
    st.title("🤖 Resume Intelligence Assistant")
    st.markdown(f"*Welcome back, {st.session_state.user_info['full_name']}!*")

    # Header with user info and logout
    col1, col2 = st.columns([3, 1])
    with col2:
        if st.button("Logout"):
            logout()

    # Sidebar for actions
    with st.sidebar:
        st.header("Actions")

        # Upload Resume
        st.subheader("📄 Upload Resume")
        uploaded_file = st.file_uploader("Upload Resume", type=["pdf", "docx"])

        if st.button("Process Resume") and uploaded_file:
            with st.spinner("Processing your resume..."):
                try:
                    files = {"file": (uploaded_file.name, uploaded_file.getvalue())}

                    response = make_authenticated_request(
                        "POST", "/upload_resume",
                        files=files
                    )

                    if response and response.status_code == 200:
                        result = safe_json_response(response)
                        if result and result.get("session_id"):
                            st.session_state.session_id = result["session_id"]

                            st.success(f"Resume processed successfully!")
                            st.info(f"Session ID: {result['session_id']}")

                            # Show extracted information
                            if result.get("parsed"):
                                with st.expander("📄 Extracted Information"):
                                    parsed_data = result["parsed"]
                                    if parsed_data.get("name"):
                                        st.write(f"**Name:** {parsed_data['name']}")
                                    if parsed_data.get("email"):
                                        st.write(f"**Email:** {parsed_data['email']}")
                                    if parsed_data.get("phone"):
                                        st.write(f"**Phone:** {parsed_data['phone']}")
                                    if parsed_data.get("Technical Skills"):
                                        st.write(f"**Skills:** {', '.join(parsed_data['Technical Skills'])}")
                        else:
                            st.error("Invalid response from server")

                        st.rerun()
                    else:
                        error_result = safe_json_response(response) if response else {}
                        error_detail = error_result.get('detail', 'Unknown error') if error_result else 'Connection failed'
                        st.error(f"Error: {error_detail}")

                except Exception as e:
                    st.error(f"Error processing resume: {str(e)}")

        # Show current session info
        if st.session_state.session_id:
            st.success(f"✅ Active Session")
            st.write(f"**Session ID:** {st.session_state.session_id[:8]}...")

        # User info
        st.markdown("---")
        st.subheader("👤 User Info")
        st.write(f"**Name:** {st.session_state.user_info['full_name']}")
        st.write(f"**Email:** {st.session_state.user_info['email']}")

    # Main content area
    if not st.session_state.session_id:
        st.info("👈 Please upload your resume from the sidebar to get started.")

        # Show demo information
        col1, col2, col3 = st.columns(3)

        with col1:
            st.subheader("📄 Resume Analysis")
            st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

        with col2:
            st.subheader("🛠 Improvement Suggestions")
            st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

        with col3:
            st.subheader("💼 Job Recommendations")
            st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

    else:
        # Action buttons interface for active session
        st.header(f"🤖 Resume Intelligence Assistant")
        st.subheader("What would you like me to help you with?")

        # Action buttons in a grid
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔍 Find Job Recommendations", use_container_width=True):
                with st.spinner("Finding job recommendations..."):
                    try:
                        response = make_authenticated_request(
                            "POST", "/chat",
                            json={
                                "user_input": "Find job recommendations for me",
                                "session_id": st.session_state.session_id
                            }
                        )
                        if response and response.status_code == 200:
                            result = safe_json_response(response)
                            if result and result.get("message"):
                                st.success(result["message"])
                            if result and result.get("data", {}).get("jobs"):
                                st.subheader("💼 Job Recommendations")
                                jobs = result["data"]["jobs"]

                                # Display job count
                                st.info(f"Found {len(jobs)} relevant job opportunities")

                                for i, job in enumerate(jobs, 1):
                                    if isinstance(job, dict):
                                        with st.container():
                                            col1, col2 = st.columns([3, 1])

                                            with col1:
                                                # Job title and company
                                                title = job.get('title', 'Job Position')
                                                company = job.get('company', 'Company')
                                                source = job.get('source', 'Job Board')

                                                if job.get('url'):
                                                    st.markdown(f"**{i}. [{title}]({job['url']})**")
                                                else:
                                                    st.markdown(f"**{i}. {title}**")

                                                st.write(f"🏢 {company} | 📍 {source}")

                                                # Job description preview
                                                if job.get('content'):
                                                    st.write(f"📝 {job['content'][:150]}...")

                                            with col2:
                                                # Relevance score if available
                                                if job.get('relevance_score'):
                                                    score = job['relevance_score']
                                                    color = "green" if score >= 80 else "orange" if score >= 60 else "red"
                                                    st.markdown(f"<div style='text-align: center; color: {color}; font-weight: bold;'>Match: {score}%</div>", unsafe_allow_html=True)

                                            st.markdown("---")
                                    else:
                                        st.write(f"• {job}")
                        else:
                            st.error("Error getting job recommendations")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

            if st.button("📊 Show Resume Data", use_container_width=True):
                with st.spinner("Loading resume data..."):
                    try:
                        response = make_authenticated_request(
                            "POST", "/chat",
                            json={
                                "user_input": "Show me my parsed resume data",
                                "session_id": st.session_state.session_id
                            }
                        )
                        if response and response.status_code == 200:
                            result = safe_json_response(response)
                            if result and result.get("message"):
                                st.success(result["message"])
                            if result and result.get("data", {}).get("parsed"):
                                st.subheader("📄 Your Resume Data")
                                st.json(result["data"]["parsed"])
                        else:
                            st.error("Error loading resume data")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

        with col2:
            if st.button("📈 Improve My Resume", use_container_width=True):
                with st.spinner("Analyzing your resume..."):
                    try:
                        response = make_authenticated_request(
                            "POST", "/chat",
                            json={
                                "user_input": "Give me suggestions to improve my resume",
                                "session_id": st.session_state.session_id
                            }
                        )
                        if response and response.status_code == 200:
                            result = safe_json_response(response)
                            if result and result.get("message"):
                                st.success(result["message"])
                            if result and result.get("data", {}).get("improvements"):
                                st.subheader("🛠 Improvement Suggestions")
                                for improvement in result["data"]["improvements"]:
                                    st.write(f"• {improvement}")
                        else:
                            st.error("Error getting improvement suggestions")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

            if st.button("❓ Get Help", use_container_width=True):
                with st.spinner("Loading help information..."):
                    try:
                        response = make_authenticated_request(
                            "POST", "/chat",
                            json={
                                "user_input": "What can you help me with?",
                                "session_id": st.session_state.session_id
                            }
                        )
                        if response and response.status_code == 200:
                            result = safe_json_response(response)
                            if result and result.get("message"):
                                st.info(result["message"])
                        else:
                            st.error("Error loading help information")
                    except Exception as e:
                        st.error(f"Error: {str(e)}")

        # Show session statistics
        st.markdown("---")
        st.subheader("📊 Session Information")
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"**User:** {st.session_state.user_info['full_name']}")
            st.write(f"**Email:** {st.session_state.user_info['email']}")
        with col2:
            st.write(f"**Session ID:** {st.session_state.session_id[:8]}...")
            st.write(f"**Status:** Resume Uploaded ✅")

    # Footer
    st.markdown("---")
    st.markdown("*Resume Intelligence Assistant - Powered by AI with JWT Authentication*")
